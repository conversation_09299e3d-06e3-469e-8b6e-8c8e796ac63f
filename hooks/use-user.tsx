"use client"

import { createContext, useContext, useEffect, useState, ReactNode } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import { supabase, getCurrentUser, getProfile } from "@/lib/supabase"

interface UserProfile {
  id: string
  username: string
  display_name: string | null
  bio: string | null
  avatar_url: string | null
  banner_url: string | null
  wallet_address: string | null
  is_verified: boolean
  is_creator: boolean
  follower_count: number
  following_count: number
  video_count: number
  nft_count: number
  total_earnings: number
  social_links: any
  preferences: any
  created_at: string
  updated_at: string
}

interface UserContextType {
  user: any | null // Supabase auth user
  profile: UserProfile | null
  isLoading: boolean
  isConnected: boolean
  walletAddress: string | null
  refreshProfile: () => Promise<void>
  signOut: () => Promise<void>
}

const UserContext = createContext<UserContextType | undefined>(undefined)

export function UserProvider({ children }: { children: ReactNode }) {
  const { connected, publicKey } = useWallet()
  const [user, setUser] = useState<any>(null)
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const refreshProfile = async () => {
    try {
      const currentUser = await getCurrentUser()
      setUser(currentUser)
      
      if (currentUser) {
        const userProfile = await getProfile(currentUser.id)
        setProfile(userProfile)
      } else {
        setProfile(null)
      }
    } catch (error) {
      console.error("Error fetching user profile:", error)
      setUser(null)
      setProfile(null)
    } finally {
      setIsLoading(false)
    }
  }

  const signOut = async () => {
    try {
      await supabase.auth.signOut()
      setUser(null)
      setProfile(null)
    } catch (error) {
      console.error("Error signing out:", error)
    }
  }

  // Listen for auth changes
  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
          await refreshProfile()
        } else if (event === 'SIGNED_OUT') {
          setUser(null)
          setProfile(null)
          setIsLoading(false)
        }
      }
    )

    // Initial load
    refreshProfile()

    return () => subscription.unsubscribe()
  }, [])

  // Update wallet address in profile when wallet connects/disconnects
  useEffect(() => {
    const updateWalletAddress = async () => {
      if (user && profile && connected && publicKey) {
        const walletAddress = publicKey.toBase58()
        if (profile.wallet_address !== walletAddress) {
          try {
            await supabase
              .from('profiles')
              .update({ wallet_address: walletAddress })
              .eq('id', user.id)
            
            setProfile(prev => prev ? { ...prev, wallet_address: walletAddress } : null)
          } catch (error) {
            console.error("Error updating wallet address:", error)
          }
        }
      } else if (user && profile && !connected && profile.wallet_address) {
        // Clear wallet address when disconnected
        try {
          await supabase
            .from('profiles')
            .update({ wallet_address: null })
            .eq('id', user.id)
          
          setProfile(prev => prev ? { ...prev, wallet_address: null } : null)
        } catch (error) {
          console.error("Error clearing wallet address:", error)
        }
      }
    }

    updateWalletAddress()
  }, [connected, publicKey, user, profile])

  const value: UserContextType = {
    user,
    profile,
    isLoading,
    isConnected: !!user,
    walletAddress: publicKey?.toBase58() || null,
    refreshProfile,
    signOut,
  }

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  )
}

export function useUser() {
  const context = useContext(UserContext)
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider')
  }
  return context
}

// Helper function to get avatar URL with fallback
export function getAvatarUrl(profile: UserProfile | null, size: number = 40): string {
  if (profile?.avatar_url) {
    return profile.avatar_url
  }
  
  // Generate a placeholder avatar based on username or wallet address
  const identifier = profile?.username || profile?.wallet_address || 'user'
  const colors = [
    'bg-gradient-to-br from-blue-400 to-purple-600',
    'bg-gradient-to-br from-green-400 to-blue-600',
    'bg-gradient-to-br from-purple-400 to-pink-600',
    'bg-gradient-to-br from-yellow-400 to-orange-600',
    'bg-gradient-to-br from-red-400 to-pink-600',
    'bg-gradient-to-br from-indigo-400 to-purple-600',
  ]
  
  const colorIndex = identifier.charCodeAt(0) % colors.length
  return `data:image/svg+xml,${encodeURIComponent(`
    <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#60a5fa;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#a855f7;stop-opacity:1" />
        </linearGradient>
      </defs>
      <circle cx="${size/2}" cy="${size/2}" r="${size/2}" fill="url(#grad)" />
      <text x="50%" y="50%" text-anchor="middle" dy="0.35em" fill="white" font-family="system-ui" font-size="${size * 0.4}" font-weight="bold">
        ${identifier.charAt(0).toUpperCase()}
      </text>
    </svg>
  `)}`
}
