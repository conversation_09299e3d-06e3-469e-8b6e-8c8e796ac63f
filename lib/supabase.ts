import { createClient } from '@supabase/supabase-js'
import { Database } from './database.types'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
})

// Storage bucket names
export const STORAGE_BUCKETS = {
  VIDEOS: process.env.NEXT_PUBLIC_SUPABASE_STORAGE_BUCKET_VIDEOS || 'videos',
  IMAGES: process.env.NEXT_PUBLIC_SUPABASE_STORAGE_BUCKET_IMAGES || 'images',
  AVATARS: process.env.NEXT_PUBLIC_SUPABASE_STORAGE_BUCKET_AVATARS || 'avatars',
} as const

// Helper functions for file uploads
export const uploadFile = async (
  bucket: keyof typeof STORAGE_BUCKETS,
  path: string,
  file: File,
  options?: { cacheControl?: string; upsert?: boolean }
) => {
  const { data, error } = await supabase.storage
    .from(STORAGE_BUCKETS[bucket])
    .upload(path, file, {
      cacheControl: options?.cacheControl || '3600',
      upsert: options?.upsert || false
    })

  if (error) {
    throw new Error(`Upload failed: ${error.message}`)
  }

  return data
}

export const getPublicUrl = (bucket: keyof typeof STORAGE_BUCKETS, path: string) => {
  const { data } = supabase.storage
    .from(STORAGE_BUCKETS[bucket])
    .getPublicUrl(path)

  return data.publicUrl
}

export const deleteFile = async (bucket: keyof typeof STORAGE_BUCKETS, path: string) => {
  const { error } = await supabase.storage
    .from(STORAGE_BUCKETS[bucket])
    .remove([path])

  if (error) {
    throw new Error(`Delete failed: ${error.message}`)
  }
}

// Enhanced upload with file size checking and progress
export const uploadLargeFile = async (
  bucket: keyof typeof STORAGE_BUCKETS,
  path: string,
  file: File,
  options?: {
    cacheControl?: string
    upsert?: boolean
    onProgress?: (progress: number) => void
    maxSizeMB?: number
  }
) => {
  const maxSizeMB = options?.maxSizeMB || 100 // Default 100MB limit
  const maxSizeBytes = maxSizeMB * 1024 * 1024

  // Check file size
  if (file.size > maxSizeBytes) {
    throw new Error(`File size (${(file.size / 1024 / 1024).toFixed(1)}MB) exceeds maximum allowed size of ${maxSizeMB}MB`)
  }

  // For smaller files (< 50MB), use regular upload
  if (file.size < 50 * 1024 * 1024) {
    const { data, error } = await supabase.storage
      .from(STORAGE_BUCKETS[bucket])
      .upload(path, file, {
        cacheControl: options?.cacheControl || '3600',
        upsert: options?.upsert || false
      })

    if (error) {
      throw new Error(`Upload failed: ${error.message}`)
    }

    options?.onProgress?.(100)
    return data
  }

  // For larger files, implement chunked upload simulation with progress
  return new Promise<any>((resolve, reject) => {
    const chunkSize = 5 * 1024 * 1024 // 5MB chunks
    let uploadedBytes = 0

    const uploadChunk = async () => {
      try {
        const { data, error } = await supabase.storage
          .from(STORAGE_BUCKETS[bucket])
          .upload(path, file, {
            cacheControl: options?.cacheControl || '3600',
            upsert: options?.upsert || false
          })

        if (error) {
          reject(new Error(`Upload failed: ${error.message}`))
          return
        }

        // Simulate progress for user feedback
        const progressInterval = setInterval(() => {
          uploadedBytes += chunkSize
          const progress = Math.min((uploadedBytes / file.size) * 100, 95)
          options?.onProgress?.(progress)

          if (uploadedBytes >= file.size) {
            clearInterval(progressInterval)
            options?.onProgress?.(100)
            resolve(data)
          }
        }, 200)

      } catch (error) {
        reject(error)
      }
    }

    uploadChunk()
  })
}

// Video compression helper
export const compressVideo = async (file: File, maxSizeMB: number = 50): Promise<File> => {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video')
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    video.onloadedmetadata = () => {
      // Calculate compression ratio based on file size
      const compressionRatio = Math.min(1, (maxSizeMB * 1024 * 1024) / file.size)

      // Reduce dimensions if needed
      const maxWidth = 1280
      const maxHeight = 720

      let { videoWidth, videoHeight } = video

      if (videoWidth > maxWidth || videoHeight > maxHeight) {
        const aspectRatio = videoWidth / videoHeight
        if (videoWidth > videoHeight) {
          videoWidth = maxWidth
          videoHeight = maxWidth / aspectRatio
        } else {
          videoHeight = maxHeight
          videoWidth = maxHeight * aspectRatio
        }
      }

      canvas.width = videoWidth
      canvas.height = videoHeight

      // For now, return original file as compression is complex
      // In production, you'd use a library like FFmpeg.wasm
      resolve(file)
    }

    video.onerror = () => reject(new Error('Failed to load video for compression'))
    video.src = URL.createObjectURL(file)
  })
}

// Real-time subscriptions
export const subscribeToVideoUpdates = (callback: (payload: any) => void) => {
  return supabase
    .channel('video-updates')
    .on('postgres_changes', 
      { event: '*', schema: 'public', table: 'videos' }, 
      callback
    )
    .subscribe()
}

export const subscribeToMessages = (conversationId: string, callback: (payload: any) => void) => {
  return supabase
    .channel(`messages-${conversationId}`)
    .on('postgres_changes', 
      { 
        event: 'INSERT', 
        schema: 'public', 
        table: 'messages',
        filter: `conversation_id=eq.${conversationId}`
      }, 
      callback
    )
    .subscribe()
}

export const subscribeToNotifications = (userId: string, callback: (payload: any) => void) => {
  return supabase
    .channel(`notifications-${userId}`)
    .on('postgres_changes', 
      { 
        event: 'INSERT', 
        schema: 'public', 
        table: 'notifications',
        filter: `user_id=eq.${userId}`
      }, 
      callback
    )
    .subscribe()
}

// Auth helpers
export const signUp = async (email: string, password: string, metadata?: any) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: metadata
    }
  })

  if (error) {
    throw new Error(`Sign up failed: ${error.message}`)
  }

  return data
}

export const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  })

  if (error) {
    throw new Error(`Sign in failed: ${error.message}`)
  }

  return data
}

export const signOut = async () => {
  const { error } = await supabase.auth.signOut()

  if (error) {
    throw new Error(`Sign out failed: ${error.message}`)
  }
}

export const getCurrentUser = async () => {
  const { data: { user }, error } = await supabase.auth.getUser()

  if (error) {
    throw new Error(`Get user failed: ${error.message}`)
  }

  return user
}

// Database helpers
export const getProfile = async (userId: string) => {
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single()

  if (error) {
    throw new Error(`Get profile failed: ${error.message}`)
  }

  return data
}

export const updateProfile = async (userId: string, updates: any) => {
  const { data, error } = await supabase
    .from('profiles')
    .update(updates)
    .eq('id', userId)
    .select()
    .single()

  if (error) {
    throw new Error(`Update profile failed: ${error.message}`)
  }

  return data
}

export const getVideos = async (filters?: {
  category?: string
  isPublic?: boolean
  creatorId?: string
  limit?: number
  offset?: number
}) => {
  let query = supabase
    .from('videos')
    .select(`
      *,
      creator:profiles(id, username, display_name, avatar_url, is_verified),
      nft:nfts(*)
    `)

  if (filters?.category) {
    query = query.eq('category', filters.category)
  }

  if (filters?.isPublic !== undefined) {
    query = query.eq('is_public', filters.isPublic)
  }

  if (filters?.creatorId) {
    query = query.eq('creator_id', filters.creatorId)
  }

  query = query
    .order('created_at', { ascending: false })
    .limit(filters?.limit || 20)

  if (filters?.offset) {
    query = query.range(filters.offset, filters.offset + (filters.limit || 20) - 1)
  }

  const { data, error } = await query

  if (error) {
    throw new Error(`Get videos failed: ${error.message}`)
  }

  return data
}

export const createVideo = async (videoData: any) => {
  const { data, error } = await supabase
    .from('videos')
    .insert(videoData)
    .select()
    .single()

  if (error) {
    throw new Error(`Create video failed: ${error.message}`)
  }

  return data
}

export const getConversations = async (userId: string) => {
  const { data, error } = await supabase
    .from('conversations')
    .select(`
      *,
      last_message:messages(content, created_at, sender:profiles(username, display_name, avatar_url))
    `)
    .contains('participant_ids', [userId])
    .order('last_message_at', { ascending: false })

  if (error) {
    throw new Error(`Get conversations failed: ${error.message}`)
  }

  return data
}

export const getMessages = async (conversationId: string, limit = 50) => {
  const { data, error } = await supabase
    .from('messages')
    .select(`
      *,
      sender:profiles(id, username, display_name, avatar_url)
    `)
    .eq('conversation_id', conversationId)
    .order('created_at', { ascending: false })
    .limit(limit)

  if (error) {
    throw new Error(`Get messages failed: ${error.message}`)
  }

  return data?.reverse() || []
}

export const sendMessage = async (messageData: any) => {
  const { data, error } = await supabase
    .from('messages')
    .insert(messageData)
    .select()
    .single()

  if (error) {
    throw new Error(`Send message failed: ${error.message}`)
  }

  return data
}
