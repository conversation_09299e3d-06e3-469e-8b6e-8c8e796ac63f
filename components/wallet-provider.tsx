"use client"

import { WalletProvider as SuietWalletProvider } from "@suiet/wallet-kit"
import type { ReactNode } from "react"

// Custom styles for wallet components to integrate with dark theme
const walletStyles = `
  /* Suiet Wallet Kit Modal Styles */
  .suiet-modal-overlay {
    background: rgba(0, 0, 0, 0.8) !important;
    backdrop-filter: blur(4px);
  }

  .suiet-modal-content {
    background: rgb(24, 24, 27) !important;
    border: 1px solid rgb(63, 63, 70) !important;
    border-radius: 12px !important;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
  }

  .suiet-modal-header {
    color: rgb(244, 244, 245) !important;
    border-bottom: 1px solid rgb(63, 63, 70) !important;
  }

  .suiet-modal-title {
    color: rgb(244, 244, 245) !important;
    font-weight: 600 !important;
  }

  .suiet-wallet-item {
    background: rgb(39, 39, 42) !important;
    border: 1px solid rgb(63, 63, 70) !important;
    border-radius: 8px !important;
    color: rgb(244, 244, 245) !important;
    transition: all 0.2s ease !important;
  }

  .suiet-wallet-item:hover {
    background: rgb(63, 63, 70) !important;
    border-color: rgb(96, 165, 250) !important;
  }

  .suiet-wallet-name {
    color: rgb(244, 244, 245) !important;
    font-weight: 500 !important;
  }

  .suiet-wallet-status {
    color: rgb(156, 163, 175) !important;
  }

  .suiet-close-button {
    color: rgb(156, 163, 175) !important;
    background: transparent !important;
    border: none !important;
  }

  .suiet-close-button:hover {
    color: rgb(244, 244, 245) !important;
    background: rgb(63, 63, 70) !important;
  }

  /* Legacy wallet adapter styles for compatibility */
  .wallet-adapter-dropdown {
    position: relative;
    display: inline-block;
  }

  .wallet-adapter-button {
    background-color: transparent;
    border: none;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    font-family: 'Inter', sans-serif;
    font-size: 14px;
    font-weight: 600;
    height: 48px;
    line-height: 48px;
    padding: 0 24px;
    border-radius: 4px;
  }

  .wallet-adapter-modal-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 1040;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .wallet-adapter-modal-overlay {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(4px);
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 1040;
  }
`

interface WalletProviderProps {
  children: ReactNode
}

export function WalletProvider({ children }: WalletProviderProps) {
  return (
    <>
      <style jsx global>
        {walletStyles}
      </style>
      <SuietWalletProvider>{children}</SuietWalletProvider>
    </>
  )
}
