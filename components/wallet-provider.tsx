"use client"

import { useMemo } from "react"
import { <PERSON><PERSON><PERSON><PERSON>, WalletProvider as SolanaWalletProvider } from "@solana/wallet-adapter-react"
import { WalletAdapterNetwork } from "@solana/wallet-adapter-base"
import { WalletModalProvider } from "@solana/wallet-adapter-react-ui"
import { clusterApiUrl } from "@solana/web3.js"
import {
  PhantomWalletAdapter,
  SolflareWalletAdapter,
  TorusWalletAdapter,
  LedgerWalletAdapter,
} from "@solana/wallet-adapter-wallets"
import type { ReactNode } from "react"

// Import default styles for Solana wallet adapter
import "@solana/wallet-adapter-react-ui/styles.css"

// Custom styles for wallet components to integrate with dark theme
const walletStyles = `
  /* Solana Wallet Adapter Modal Styles */
  .wallet-adapter-modal-wrapper {
    background: rgba(0, 0, 0, 0.8) !important;
    backdrop-filter: blur(4px);
  }

  .wallet-adapter-modal {
    background: rgb(24, 24, 27) !important;
    border: 1px solid rgb(63, 63, 70) !important;
    border-radius: 12px !important;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
  }

  .wallet-adapter-modal-title {
    color: rgb(244, 244, 245) !important;
    font-weight: 600 !important;
  }

  .wallet-adapter-modal-list {
    background: transparent !important;
  }

  .wallet-adapter-modal-list-more {
    background: rgb(39, 39, 42) !important;
    border: 1px solid rgb(63, 63, 70) !important;
    border-radius: 8px !important;
    color: rgb(244, 244, 245) !important;
  }

  .wallet-adapter-modal-list-more:hover {
    background: rgb(63, 63, 70) !important;
    border-color: rgb(96, 165, 250) !important;
  }

  .wallet-adapter-modal-list li {
    background: rgb(39, 39, 42) !important;
    border: 1px solid rgb(63, 63, 70) !important;
    border-radius: 8px !important;
    margin-bottom: 8px !important;
  }

  .wallet-adapter-modal-list li:hover {
    background: rgb(63, 63, 70) !important;
    border-color: rgb(96, 165, 250) !important;
  }

  .wallet-adapter-modal-list li button {
    color: rgb(244, 244, 245) !important;
    font-weight: 500 !important;
  }

  .wallet-adapter-modal-button-close {
    color: rgb(156, 163, 175) !important;
    background: transparent !important;
  }

  .wallet-adapter-modal-button-close:hover {
    color: rgb(244, 244, 245) !important;
    background: rgb(63, 63, 70) !important;
  }

  .wallet-adapter-button {
    background-color: transparent;
    border: none;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    font-family: 'Inter', sans-serif;
    font-size: 14px;
    font-weight: 600;
    height: 48px;
    line-height: 48px;
    padding: 0 24px;
    border-radius: 4px;
  }

  .wallet-adapter-button:not([disabled]):hover {
    background-color: rgb(63, 63, 70);
  }

  .wallet-adapter-dropdown {
    position: relative;
    display: inline-block;
  }
`

interface WalletProviderProps {
  children: ReactNode
}

export function WalletProvider({ children }: WalletProviderProps) {
  // The network can be set to 'devnet', 'testnet', or 'mainnet-beta'
  const network = WalletAdapterNetwork.Devnet

  // You can also provide a custom RPC endpoint
  const endpoint = useMemo(() => clusterApiUrl(network), [network])

  const wallets = useMemo(
    () => [
      new PhantomWalletAdapter(),
      new SolflareWalletAdapter(),
      new TorusWalletAdapter(),
      new LedgerWalletAdapter(),
    ],
    []
  )

  return (
    <>
      <style jsx global>
        {walletStyles}
      </style>
      <ConnectionProvider endpoint={endpoint}>
        <SolanaWalletProvider wallets={wallets} autoConnect>
          <WalletModalProvider>
            {children}
          </WalletModalProvider>
        </SolanaWalletProvider>
      </ConnectionProvider>
    </>
  )
}
