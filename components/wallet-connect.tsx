"use client"

import { useState, useRef, useEffect } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import { WalletMultiButton } from "@solana/wallet-adapter-react-ui"
import { ChevronDown, User, LogOut, Copy, Check } from "lucide-react"

interface WalletConnectProps {
  className?: string
  iconOnly?: boolean
}

export function WalletConnect({ className = "", iconOnly = false }: WalletConnectProps) {
  const { connected, disconnect, publicKey } = useWallet()
  const [isLoading, setIsLoading] = useState(false)
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [addressCopied, setAddressCopied] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleDisconnect = async () => {
    try {
      setIsLoading(true)
      await disconnect()
      setIsDropdownOpen(false)
    } catch (error) {
      console.error("Failed to disconnect wallet:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCopyAddress = async () => {
    if (publicKey) {
      try {
        await navigator.clipboard.writeText(publicKey.toBase58())
        setAddressCopied(true)
        setTimeout(() => setAddressCopied(false), 2000)
      } catch (error) {
        console.error("Failed to copy address:", error)
      }
    }
  }

  const formatAddress = (address: string) => {
    return `${address.slice(0, 4)}...${address.slice(-4)}`
  }

  // For mobile menu
  if (!iconOnly && !connected) {
    return (
      <div className="w-full">
        <WalletMultiButton className="!w-full !h-10 !flex !items-center !justify-center !rounded-full !bg-gradient-to-r !from-blue-500 !to-purple-600 hover:!from-blue-600 hover:!to-purple-700 !text-white !font-medium !border-0">
          <WalletIcon className="h-5 w-5" />
        </WalletMultiButton>
      </div>
    )
  }

  // For mobile menu when connected
  if (!iconOnly && connected) {
    return (
      <button
        onClick={handleDisconnect}
        disabled={isLoading}
        className="w-full h-10 flex items-center justify-center gap-2 rounded-full border border-red-500/50 text-red-400 hover:bg-red-950/20 disabled:opacity-50"
      >
        {isLoading ? "Disconnecting..." : "Disconnect Wallet"}
      </button>
    )
  }

  return (
    <div className={className}>
      {!connected ? (
        <WalletMultiButton className="!flex !items-center !justify-center !w-9 !h-9 !rounded-full !bg-gradient-to-b !from-blue-400 !to-purple-600 hover:!from-blue-500 hover:!to-purple-700 !text-white !transition-all !duration-300 !shadow-lg !shadow-purple-500/20 !border !border-blue-300/20 !relative !overflow-hidden !p-0 !min-w-0">
          <WalletIcon className="h-4 w-4" />
        </WalletMultiButton>
      ) : (
        <div className="hidden md:flex items-center gap-3">
          <div className="bg-zinc-800 rounded-full px-3 py-1 text-xs font-medium flex items-center gap-1.5">
            <div className="w-2 h-2 rounded-full bg-blue-400 animate-pulse"></div>
            <span className="text-blue-400">Connected</span>
          </div>

          {/* Desktop Avatar Dropdown */}
          <div className="relative" ref={dropdownRef}>
            <button
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              className="flex items-center gap-2 p-1 rounded-full bg-zinc-800 border border-zinc-700 hover:bg-zinc-700 transition-colors"
            >
              <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-400 to-purple-600 flex items-center justify-center text-xs font-bold text-white shadow-lg">
                {publicKey ? publicKey.toBase58().substring(0, 1).toUpperCase() : "?"}
              </div>
              <ChevronDown className={`h-4 w-4 text-zinc-400 transition-transform duration-200 ${isDropdownOpen ? 'rotate-180' : ''}`} />
            </button>

            {/* Dropdown Menu */}
            {isDropdownOpen && (
              <div className="absolute right-0 top-full mt-2 w-64 bg-zinc-900 border border-zinc-700 rounded-lg shadow-xl shadow-black/20 z-50">
                <div className="p-3 border-b border-zinc-700">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-400 to-purple-600 flex items-center justify-center text-sm font-bold text-white shadow-lg">
                      {publicKey ? publicKey.toBase58().substring(0, 1).toUpperCase() : "?"}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-white">Wallet Connected</p>
                      <div className="flex items-center gap-2 mt-1">
                        <p className="text-xs text-zinc-400 font-mono">
                          {publicKey ? formatAddress(publicKey.toBase58()) : ""}
                        </p>
                        <button
                          onClick={handleCopyAddress}
                          className="p-1 hover:bg-zinc-700 rounded transition-colors"
                          title="Copy address"
                        >
                          {addressCopied ? (
                            <Check className="h-3 w-3 text-green-400" />
                          ) : (
                            <Copy className="h-3 w-3 text-zinc-400" />
                          )}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-1">
                  <a
                    href="/profile"
                    className="flex items-center gap-3 px-3 py-2 text-sm text-zinc-300 hover:bg-zinc-800 rounded-md transition-colors"
                    onClick={() => setIsDropdownOpen(false)}
                  >
                    <User className="h-4 w-4" />
                    View Profile
                  </a>

                  <button
                    onClick={handleDisconnect}
                    disabled={isLoading}
                    className="w-full flex items-center gap-3 px-3 py-2 text-sm text-red-400 hover:bg-red-950/20 rounded-md transition-colors disabled:opacity-50"
                  >
                    <LogOut className="h-4 w-4" />
                    {isLoading ? "Disconnecting..." : "Disconnect Wallet"}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

function WalletIcon({ className = "" }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="M21 12V7H5a2 2 0 0 1 0-4h14v4" />
      <path d="M3 5v14a2 2 0 0 0 2 2h16v-5" />
      <path d="M18 12a2 2 0 0 0 0 4h4v-4Z" />
    </svg>
  )
}
