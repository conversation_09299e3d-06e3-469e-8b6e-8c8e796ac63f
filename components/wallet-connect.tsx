"use client"

import { useState } from "react"
import { useWallet } from "@suiet/wallet-kit"
import { ConnectButton } from "@suiet/wallet-kit"

interface WalletConnectProps {
  className?: string
  iconOnly?: boolean
}

export function WalletConnect({ className = "", iconOnly = false }: WalletConnectProps) {
  const { connected, connecting, select, disconnect, account } = useWallet()
  const [isLoading, setIsLoading] = useState(false)

  // Update the handleConnect function to remove MetaMask and focus only on Sui wallets
  const handleConnect = async () => {
    try {
      setIsLoading(true)
      // Only use Sui-compatible wallets
      const availableWallets = ["Sui", "Suiet", "Ethos", "Surf"]

      for (const wallet of availableWallets) {
        try {
          await select(wallet)
          console.log(`Connected to ${wallet} wallet`)
          break
        } catch (err) {
          console.log(`${wallet} wallet not available, trying next...`)
        }
      }
    } catch (error) {
      console.error("Failed to connect wallet:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleDisconnect = async () => {
    try {
      await disconnect()
    } catch (error) {
      console.error("Failed to disconnect wallet:", error)
    }
  }

  // For mobile menu
  if (!iconOnly && !connected) {
    return (
      <ConnectButton className="w-full h-10 flex items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-medium">
        <WalletIcon className="h-5 w-5" />
      </ConnectButton>
    )
  }

  // For mobile menu when connected
  if (!iconOnly && connected) {
    return (
      <button
        onClick={handleDisconnect}
        className="w-full h-10 flex items-center justify-center gap-2 rounded-full border border-red-500/50 text-red-400 hover:bg-red-950/20"
      >
        Disconnect Wallet
      </button>
    )
  }

  return (
    <div className={className}>
      {!connected ? (
        <ConnectButton className="flex items-center justify-center w-9 h-9 rounded-full bg-gradient-to-b from-blue-400 to-purple-600 hover:from-blue-500 hover:to-purple-700 text-white transition-all duration-300 shadow-lg shadow-purple-500/20 border border-blue-300/20 relative overflow-hidden">
          <WalletIcon className="h-4 w-4" />
        </ConnectButton>
      ) : (
        <div className="hidden md:flex items-center gap-3">
          <div className="bg-zinc-800 rounded-full px-3 py-1 text-xs font-medium flex items-center gap-1.5">
            <div className="w-2 h-2 rounded-full bg-blue-400 animate-pulse"></div>
            <span className="text-blue-400">Connected</span>
          </div>
          <button
            onClick={handleDisconnect}
            className="w-8 h-8 rounded-full bg-zinc-800 flex items-center justify-center border border-zinc-700 hover:bg-zinc-700 transition-colors"
          >
            <div className="w-6 h-6 rounded-full bg-gradient-to-r from-blue-400 to-purple-600 flex items-center justify-center text-xs font-bold text-white">
              {account?.address ? account.address.substring(0, 1).toUpperCase() : "?"}
            </div>
          </button>
        </div>
      )}
    </div>
  )
}

function WalletIcon({ className = "" }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="M21 12V7H5a2 2 0 0 1 0-4h14v4" />
      <path d="M3 5v14a2 2 0 0 0 2 2h16v-5" />
      <path d="M18 12a2 2 0 0 0 0 4h4v-4Z" />
    </svg>
  )
}
