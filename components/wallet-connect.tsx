"use client"

import { useState } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import { WalletMultiButton } from "@solana/wallet-adapter-react-ui"

interface WalletConnectProps {
  className?: string
  iconOnly?: boolean
}

export function WalletConnect({ className = "", iconOnly = false }: WalletConnectProps) {
  const { connected, connecting, disconnect, publicKey } = useWallet()
  const [isLoading, setIsLoading] = useState(false)

  const handleDisconnect = async () => {
    try {
      setIsLoading(true)
      await disconnect()
    } catch (error) {
      console.error("Failed to disconnect wallet:", error)
    } finally {
      setIsLoading(false)
    }
  }

  // For mobile menu
  if (!iconOnly && !connected) {
    return (
      <div className="w-full">
        <WalletMultiButton className="!w-full !h-10 !flex !items-center !justify-center !rounded-full !bg-gradient-to-r !from-blue-500 !to-purple-600 hover:!from-blue-600 hover:!to-purple-700 !text-white !font-medium !border-0">
          <WalletIcon className="h-5 w-5" />
        </WalletMultiButton>
      </div>
    )
  }

  // For mobile menu when connected
  if (!iconOnly && connected) {
    return (
      <button
        onClick={handleDisconnect}
        disabled={isLoading}
        className="w-full h-10 flex items-center justify-center gap-2 rounded-full border border-red-500/50 text-red-400 hover:bg-red-950/20 disabled:opacity-50"
      >
        {isLoading ? "Disconnecting..." : "Disconnect Wallet"}
      </button>
    )
  }

  return (
    <div className={className}>
      {!connected ? (
        <WalletMultiButton className="!flex !items-center !justify-center !w-9 !h-9 !rounded-full !bg-gradient-to-b !from-blue-400 !to-purple-600 hover:!from-blue-500 hover:!to-purple-700 !text-white !transition-all !duration-300 !shadow-lg !shadow-purple-500/20 !border !border-blue-300/20 !relative !overflow-hidden !p-0 !min-w-0">
          <WalletIcon className="h-4 w-4" />
        </WalletMultiButton>
      ) : (
        <div className="hidden md:flex items-center gap-3">
          <div className="bg-zinc-800 rounded-full px-3 py-1 text-xs font-medium flex items-center gap-1.5">
            <div className="w-2 h-2 rounded-full bg-blue-400 animate-pulse"></div>
            <span className="text-blue-400">Connected</span>
          </div>
          <button
            onClick={handleDisconnect}
            disabled={isLoading}
            className="w-8 h-8 rounded-full bg-zinc-800 flex items-center justify-center border border-zinc-700 hover:bg-zinc-700 transition-colors disabled:opacity-50"
          >
            <div className="w-6 h-6 rounded-full bg-gradient-to-r from-blue-400 to-purple-600 flex items-center justify-center text-xs font-bold text-white">
              {publicKey ? publicKey.toBase58().substring(0, 1).toUpperCase() : "?"}
            </div>
          </button>
        </div>
      )}
    </div>
  )
}

function WalletIcon({ className = "" }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="M21 12V7H5a2 2 0 0 1 0-4h14v4" />
      <path d="M3 5v14a2 2 0 0 0 2 2h16v-5" />
      <path d="M18 12a2 2 0 0 0 0 4h4v-4Z" />
    </svg>
  )
}
