"use client"

import { useState, useEffect } from "react"
import { use<PERSON>ara<PERSON> } from "next/navigation"
import { ThumbsUp, ThumbsDown, Eye, Share2, Download, Flag, ChevronDown, ChevronUp } from "lucide-react"
import VideoPlayer from "@/components/video-player"
import Header from "@/components/header"
import VideoGrid from "@/components/video-grid"
import { Button } from "@/components/ui/button"
import { supabase } from "@/lib/supabase"
import { useUser } from "@/hooks/use-user"
import { toast } from "sonner"

interface Video {
  id: string
  title: string
  description: string | null
  category: string
  video_url: string
  thumbnail_url: string | null
  duration: number | null
  creator_id: string
  view_count: number
  like_count: number
  is_nft: boolean
  created_at: string
  profiles: {
    username: string
    display_name: string | null
    avatar_url: string | null
    is_verified: boolean
  }
}

interface Comment {
  id: string
  content: string
  created_at: string
  like_count: number
  user_id: string
  profiles: {
    username: string
    display_name: string | null
    avatar_url: string | null
    is_verified: boolean
  }
}

// Helper function to format duration
function formatDuration(seconds: number | null): string {
  if (!seconds) return "0:00"
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

// Helper function to format view count
function formatViewCount(count: number): string {
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M`
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}K`
  }
  return count.toString()
}

// Helper function to transform video for VideoGrid
function transformVideo(video: Video) {
  return {
    id: video.id,
    title: video.title,
    description: video.description || "No description available",
    thumbnail: video.thumbnail_url || "/placeholder.svg?height=720&width=1280",
    duration: formatDuration(video.duration),
    creator: video.profiles.display_name || video.profiles.username,
    isNft: video.is_nft,
    rarity: video.is_nft ? "rare" as const : undefined,
    views: video.view_count,
    likes: video.like_count,
    videoUrl: video.video_url,
    creatorAvatar: video.profiles.avatar_url,
    isVerified: video.profiles.is_verified,
  }
}

export default function VideoPage() {
  const params = useParams()
  const videoId = params.id as string
  const { profile, isConnected } = useUser()
  
  const [video, setVideo] = useState<Video | null>(null)
  const [comments, setComments] = useState<Comment[]>([])
  const [creatorVideos, setCreatorVideos] = useState<Video[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showDetails, setShowDetails] = useState(false)
  const [newComment, setNewComment] = useState("")
  const [isSubmittingComment, setIsSubmittingComment] = useState(false)
  const [hasLiked, setHasLiked] = useState(false)
  const [hasDisliked, setHasDisliked] = useState(false)
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0)
  const [autoPlay, setAutoPlay] = useState(false)

  // Fetch video data
  useEffect(() => {
    const fetchVideo = async () => {
      if (!videoId) return

      try {
        setIsLoading(true)

        // Fetch video details
        const { data: videoData, error: videoError } = await supabase
          .from('videos')
          .select(`
            *,
            profiles:creator_id (
              username,
              display_name,
              avatar_url,
              is_verified
            )
          `)
          .eq('id', videoId)
          .single()

        if (videoError) {
          console.error('Error fetching video:', videoError)
          toast.error('Video not found')
          return
        }

        setVideo(videoData as Video)

        // Increment view count
        await supabase
          .from('videos')
          .update({ view_count: (videoData.view_count || 0) + 1 })
          .eq('id', videoId)

        // Fetch comments
        const { data: commentsData, error: commentsError } = await supabase
          .from('comments')
          .select(`
            *,
            profiles:user_id (
              username,
              display_name,
              avatar_url,
              is_verified
            )
          `)
          .eq('video_id', videoId)
          .order('created_at', { ascending: false })

        if (!commentsError && commentsData) {
          setComments(commentsData as Comment[])
        }

        // Fetch more videos from the same creator
        const { data: creatorVideosData, error: creatorVideosError } = await supabase
          .from('videos')
          .select(`
            *,
            profiles:creator_id (
              username,
              display_name,
              avatar_url,
              is_verified
            )
          `)
          .eq('creator_id', videoData.creator_id)
          .neq('id', videoId)
          .eq('is_public', true)
          .order('created_at', { ascending: false })
          .limit(12)

        if (!creatorVideosError && creatorVideosData) {
          setCreatorVideos(creatorVideosData as Video[])
        }

        // Check if user has liked/disliked (if logged in)
        if (isConnected && profile) {
          const { data: likeData } = await supabase
            .from('likes')
            .select('*')
            .eq('video_id', videoId)
            .eq('user_id', profile.id)
            .single()

          if (likeData) {
            setHasLiked(true)
          }
        }

      } catch (error) {
        console.error('Error in fetchVideo:', error)
        toast.error('Failed to load video')
      } finally {
        setIsLoading(false)
      }
    }

    fetchVideo()
  }, [videoId, isConnected, profile])

  // Handle like/dislike
  const handleLike = async () => {
    if (!isConnected || !profile || !video) {
      toast.error('Please connect your wallet to like videos')
      return
    }

    try {
      if (hasLiked) {
        // Remove like
        await supabase
          .from('likes')
          .delete()
          .eq('video_id', videoId)
          .eq('user_id', profile.id)

        setHasLiked(false)
        setVideo(prev => prev ? { ...prev, like_count: prev.like_count - 1 } : null)
      } else {
        // Add like
        await supabase
          .from('likes')
          .insert({
            video_id: videoId,
            user_id: profile.id
          })

        setHasLiked(true)
        setVideo(prev => prev ? { ...prev, like_count: prev.like_count + 1 } : null)
      }
    } catch (error) {
      console.error('Error handling like:', error)
      toast.error('Failed to update like')
    }
  }

  // Handle comment submission
  const handleSubmitComment = async () => {
    if (!isConnected || !profile) {
      toast.error('Please connect your wallet to comment')
      return
    }

    if (!newComment.trim()) {
      toast.error('Please enter a comment')
      return
    }

    try {
      setIsSubmittingComment(true)

      const { data: commentData, error } = await supabase
        .from('comments')
        .insert({
          video_id: videoId,
          user_id: profile.id,
          content: newComment.trim()
        })
        .select(`
          *,
          profiles:user_id (
            username,
            display_name,
            avatar_url,
            is_verified
          )
        `)
        .single()

      if (error) {
        console.error('Error submitting comment:', error)
        toast.error('Failed to submit comment')
        return
      }

      // Add new comment to the top of the list
      setComments(prev => [commentData as Comment, ...prev])
      setNewComment("")
      toast.success('Comment added successfully')

    } catch (error) {
      console.error('Error in handleSubmitComment:', error)
      toast.error('Failed to submit comment')
    } finally {
      setIsSubmittingComment(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-black">
        <Header />
        <div className="flex items-center justify-center h-[80vh]">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-zinc-400">Loading video...</p>
          </div>
        </div>
      </div>
    )
  }

  if (!video) {
    return (
      <div className="min-h-screen bg-black">
        <Header />
        <div className="flex items-center justify-center h-[80vh]">
          <div className="text-center">
            <p className="text-zinc-400 text-lg mb-2">Video not found</p>
            <p className="text-zinc-500 text-sm">The video you're looking for doesn't exist or has been removed.</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-black">
      <Header />
      
      <main className="pt-20 pb-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Main video section */}
            <div className="lg:col-span-2 space-y-6">
              {/* Video Player */}
              <div className="aspect-video w-full">
                <VideoPlayer
                  videoSrc={video.video_url}
                  poster={video.thumbnail_url || "/placeholder.svg?height=720&width=1280"}
                  title={video.title}
                />
              </div>

              {/* Video Info */}
              <div className="space-y-4">
                <h1 className="text-2xl font-bold text-white">{video.title}</h1>
                
                {/* Stats and Actions */}
                <div className="flex flex-wrap items-center justify-between gap-4">
                  <div className="flex items-center gap-6 text-sm text-zinc-400">
                    <div className="flex items-center gap-1">
                      <Eye className="h-4 w-4" />
                      <span>{formatViewCount(video.view_count)} views</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <span>{new Date(video.created_at).toLocaleDateString()}</span>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleLike}
                      className={`flex items-center gap-2 ${hasLiked ? 'bg-blue-500/20 border-blue-500 text-blue-400' : ''}`}
                    >
                      <ThumbsUp className="h-4 w-4" />
                      <span>{video.like_count}</span>
                    </Button>
                    
                    <Button variant="outline" size="sm" className="flex items-center gap-2">
                      <Share2 className="h-4 w-4" />
                      <span>Share</span>
                    </Button>
                    
                    <Button variant="outline" size="sm" className="flex items-center gap-2">
                      <Download className="h-4 w-4" />
                      <span>Download</span>
                    </Button>
                  </div>
                </div>

                {/* Creator Info & Description */}
                <div className="bg-zinc-900/50 rounded-xl p-6 border border-zinc-700/50">
                  <div className="flex items-start gap-4 mb-4">
                    <img
                      src={video.profiles.avatar_url || "/placeholder.svg?height=48&width=48"}
                      alt={video.profiles.username}
                      className="w-12 h-12 rounded-full"
                    />
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-semibold text-white">
                          {video.profiles.display_name || video.profiles.username}
                        </h3>
                        {video.profiles.is_verified && (
                          <div className="w-5 h-5 rounded-full bg-cyan-500 flex items-center justify-center">
                            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </div>
                        )}
                      </div>
                      <p className="text-sm text-zinc-400">
                        {video.creator_id.slice(0, 4)}...{video.creator_id.slice(-4)}
                      </p>
                    </div>
                    <Button variant="outline" size="sm">
                      Follow
                    </Button>
                  </div>

                  {/* Description Toggle */}
                  <div>
                    <button
                      onClick={() => setShowDetails(!showDetails)}
                      className="flex items-center gap-2 text-sm text-zinc-400 hover:text-white transition-colors mb-2"
                    >
                      <span>Description</span>
                      {showDetails ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                    </button>

                    {showDetails && (
                      <div className="space-y-3 text-sm text-zinc-300">
                        <p>{video.description || "No description available"}</p>
                        <div className="flex flex-wrap gap-4 text-xs text-zinc-400">
                          <span>Category: {video.category}</span>
                          <span>Duration: {formatDuration(video.duration)}</span>
                          {video.is_nft && <span className="text-amber-400">NFT</span>}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Comments Section */}
                <div className="space-y-4">
                  <h2 className="text-xl font-bold text-white">
                    Comments ({comments.length})
                  </h2>

                  {/* Add Comment */}
                  {isConnected ? (
                    <div className="space-y-3">
                      <div className="flex gap-3">
                        <img
                          src={profile?.avatar_url || "/placeholder.svg?height=32&width=32"}
                          alt="Your avatar"
                          className="w-8 h-8 rounded-full"
                        />
                        <div className="flex-1">
                          <textarea
                            placeholder="Add a comment..."
                            value={newComment}
                            onChange={(e) => setNewComment(e.target.value)}
                            className="w-full min-h-[80px] bg-zinc-800 border border-zinc-700 rounded-lg px-3 py-2 text-white placeholder:text-zinc-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                          />
                        </div>
                      </div>
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setNewComment("")}
                          disabled={!newComment.trim()}
                        >
                          Cancel
                        </Button>
                        <Button
                          size="sm"
                          onClick={handleSubmitComment}
                          disabled={isSubmittingComment || !newComment.trim()}
                        >
                          {isSubmittingComment ? "Posting..." : "Comment"}
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="bg-zinc-800/50 rounded-lg p-4 text-center">
                      <p className="text-zinc-400">Connect your wallet to leave a comment</p>
                    </div>
                  )}

                  {/* Comments List */}
                  <div className="space-y-4">
                    {comments.map((comment) => (
                      <div key={comment.id} className="flex gap-3">
                        <img
                          src={comment.profiles.avatar_url || "/placeholder.svg?height=32&width=32"}
                          alt={comment.profiles.username}
                          className="w-8 h-8 rounded-full"
                        />
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium text-white text-sm">
                              {comment.profiles.display_name || comment.profiles.username}
                            </span>
                            {comment.profiles.is_verified && (
                              <div className="w-4 h-4 rounded-full bg-cyan-500 flex items-center justify-center">
                                <svg className="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                              </div>
                            )}
                            <span className="text-xs text-zinc-400">
                              {new Date(comment.created_at).toLocaleDateString()}
                            </span>
                          </div>
                          <p className="text-sm text-zinc-300 mb-2">{comment.content}</p>
                          <div className="flex items-center gap-4">
                            <button className="flex items-center gap-1 text-xs text-zinc-400 hover:text-white transition-colors">
                              <ThumbsUp className="h-3 w-3" />
                              <span>{comment.like_count}</span>
                            </button>
                            <button className="text-xs text-zinc-400 hover:text-white transition-colors">
                              Reply
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Sidebar - Related Videos */}
            <div className="lg:col-span-1">
              <div className="space-y-6">
                {/* Autoplay Toggle */}
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-bold text-white">More from {video.profiles.display_name || video.profiles.username}</h2>
                  <label className="flex items-center gap-2 text-sm text-zinc-400">
                    <input
                      type="checkbox"
                      checked={autoPlay}
                      onChange={(e) => setAutoPlay(e.target.checked)}
                      className="rounded"
                    />
                    Autoplay
                  </label>
                </div>

                {/* Related Videos */}
                {creatorVideos.length > 0 ? (
                  <div className="space-y-4">
                    {creatorVideos.map((relatedVideo, index) => (
                      <div
                        key={relatedVideo.id}
                        className="flex gap-3 p-3 rounded-lg hover:bg-zinc-800/50 transition-colors cursor-pointer"
                        onClick={() => window.location.href = `/video/${relatedVideo.id}`}
                      >
                        <div className="relative w-32 h-18 rounded-lg overflow-hidden flex-shrink-0">
                          <img
                            src={relatedVideo.thumbnail_url || "/placeholder.svg?height=72&width=128"}
                            alt={relatedVideo.title}
                            className="w-full h-full object-cover"
                          />
                          <div className="absolute bottom-1 right-1 bg-black/80 text-white text-xs px-1 rounded">
                            {formatDuration(relatedVideo.duration)}
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-medium text-white text-sm line-clamp-2 mb-1">
                            {relatedVideo.title}
                          </h3>
                          <p className="text-xs text-zinc-400 mb-1">
                            {formatViewCount(relatedVideo.view_count)} views
                          </p>
                          <p className="text-xs text-zinc-500">
                            {new Date(relatedVideo.created_at).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-zinc-400">No other videos from this creator</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
