import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import MobileNav from "@/components/mobile-nav"
import { ThemeProvider } from "next-themes"
import { WalletProvider } from "@/components/wallet-provider"
import { Toaster } from "@/components/ui/sonner"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Phlix - AI Video Platform",
  description: "Create, share, and discover AI-generated videos with Phlix",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <WalletProvider>
          <ThemeProvider attribute="class" defaultTheme="dark">
            {children}
            <MobileNav />
          </ThemeProvider>
        </WalletProvider>
      </body>
    </html>
  )
}
