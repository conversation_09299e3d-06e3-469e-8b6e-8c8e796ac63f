"use client"

import { useState } from "react"
import { <PERSON>, Sparkles, Film, Music, Heart, Palette, Camera, Smile, Lock, Zap } from "lucide-react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import VideoGrid from "@/components/video-grid"
import Header from "@/components/header"

// Mock data for different categories
const mockVideos = [
  {
    id: "1",
    title: "Cyberpunk City Dreams",
    description: "Neon-lit streets in a futuristic metropolis",
    thumbnail: "/placeholder.svg",
    duration: "2:34",
    isNft: true,
    rarity: "rare" as const,
    creator: "CyberArtist",
    price: 0.5,
  },
  {
    id: "2", 
    title: "Abstract Fluid Motion",
    description: "Mesmerizing liquid animations",
    thumbnail: "/placeholder.svg",
    duration: "1:45",
    isNft: false,
    creator: "FluidMaster",
  },
  {
    id: "3",
    title: "Robot Dance Party",
    description: "Mechanical beings in perfect harmony",
    thumbnail: "/placeholder.svg", 
    duration: "3:12",
    isNft: true,
    rarity: "legendary" as const,
    creator: "RoboChoreographer",
    price: 1.2,
  },
  {
    id: "4",
    title: "Enchanted Forest",
    description: "Magical woodland creatures",
    thumbnail: "/placeholder.svg",
    duration: "4:20",
    isNft: false,
    creator: "NatureMage",
  },
]

// Category icons mapping
const categoryIcons = {
  creative: Sparkles,
  shorts: Zap,
  comedy: Smile,
  encrypted: Lock,
  music: Music,
  art: Palette,
  film: Film,
  romance: Heart,
  anime: Camera,
  cartoon: Camera,
}

export default function DiscoverPage() {
  const [activeTab, setActiveTab] = useState("creative")
  const [searchQuery, setSearchQuery] = useState("")

  // Tab labels for accessibility
  const tabLabels = {
    creative: "Creative",
    shorts: "Shorts", 
    comedy: "Comedy",
    encrypted: "Encrypted",
    music: "Music",
    art: "Art",
    film: "Film",
    romance: "Romance",
    anime: "Anime",
    cartoon: "Cartoon",
  }

  // Filter videos based on search query (mock implementation)
  const filteredVideos = mockVideos.filter(video => 
    video.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    video.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    video.creator?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className="min-h-screen bg-zinc-950">
      <Header visible={true} />
      
      <main className="container mx-auto px-4 pt-24 pb-20">
        {/* Page Header */}
        <div className="text-center mb-12">
          <div className="relative inline-block">
            {/* Decorative elements */}
            <div className="absolute -top-8 -left-8 w-16 h-16 rounded-full bg-cyan-500/10 blur-2xl"></div>
            <div className="absolute -bottom-8 -right-8 w-16 h-16 rounded-full bg-purple-500/10 blur-2xl"></div>
            
            <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-cyan-300 via-blue-400 to-purple-500 bg-clip-text text-transparent mb-4">
              Discover
            </h1>
            <p className="text-zinc-400 text-lg max-w-2xl mx-auto">
              Explore amazing AI-generated videos across different categories and find your next favorite creator
            </p>
          </div>
        </div>

        {/* Search Bar */}
        <div className="max-w-2xl mx-auto mb-12">
          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/20 to-purple-500/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
            <div className="relative bg-zinc-900/80 backdrop-blur-sm border border-zinc-800 rounded-2xl p-1">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-zinc-400" />
                <Input
                  placeholder="Search videos, creators, collections..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-12 pr-4 py-4 bg-transparent border-0 text-white placeholder:text-zinc-400 focus-visible:ring-2 focus-visible:ring-cyan-500/50 text-lg"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Category Tabs */}
        <Tabs defaultValue="creative" className="space-y-8" onValueChange={setActiveTab}>
          <div className="flex justify-center mb-8">
            <div className="overflow-x-auto scrollbar-hide">
              <TabsList className="bg-gradient-to-r from-zinc-900 via-zinc-800 to-zinc-900 border border-cyan-500/30 rounded-full p-1.5 flex gap-1 shadow-xl shadow-cyan-500/20 backdrop-blur-sm h-auto animate-glow relative min-w-max">
                {Object.entries(tabLabels).map(([value, label]) => {
                  const IconComponent = categoryIcons[value as keyof typeof categoryIcons]
                  return (
                    <TabsTrigger
                      key={value}
                      value={value}
                      className="w-12 h-12 rounded-full flex items-center justify-center group relative data-[state=active]:bg-gradient-to-br data-[state=active]:from-cyan-400 data-[state=active]:to-blue-600 data-[state=active]:shadow-lg data-[state=active]:shadow-cyan-500/30 transition-all duration-300"
                      aria-label={label}
                    >
                      <IconComponent className="h-5 w-5 group-data-[state=active]:text-white text-zinc-400 group-hover:text-zinc-200 transition-colors" />
                      <span className="sr-only">{label}</span>
                      {activeTab === value && (
                        <span className="absolute -bottom-9 left-1/2 transform -translate-x-1/2 text-xs text-cyan-400 font-medium whitespace-nowrap">
                          {label}
                        </span>
                      )}
                    </TabsTrigger>
                  )
                })}
              </TabsList>
            </div>
          </div>

          {/* Tab Content */}
          {Object.keys(tabLabels).map((category) => (
            <TabsContent key={category} value={category} className="pt-4">
              <div className="space-y-6">
                {filteredVideos.length > 0 ? (
                  <VideoGrid videos={filteredVideos} />
                ) : (
                  <div className="text-center py-16">
                    <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-zinc-800 flex items-center justify-center">
                      <Search className="h-8 w-8 text-zinc-400" />
                    </div>
                    <h3 className="text-xl font-semibold text-zinc-300 mb-2">No videos found</h3>
                    <p className="text-zinc-500">
                      {searchQuery 
                        ? `No results for "${searchQuery}" in ${tabLabels[category as keyof typeof tabLabels]}`
                        : `No videos available in ${tabLabels[category as keyof typeof tabLabels]} category yet`
                      }
                    </p>
                  </div>
                )}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </main>
    </div>
  )
}
