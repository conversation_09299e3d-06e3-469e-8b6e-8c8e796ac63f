"use client"

import { useState, useEffect, useMemo } from "react"
import { Search, Sparkles, Film, Music, Heart, Palette, <PERSON>, Smile, <PERSON>, Zap } from "lucide-react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import HorizontalVideoGrid from "@/components/horizontal-video-grid"
import Header from "@/components/header"
import { supabase } from "@/lib/supabase"

interface Video {
  id: string
  title: string
  description: string | null
  category: string
  video_url: string
  thumbnail_url: string | null
  duration: number | null
  creator_id: string
  view_count: number
  like_count: number
  is_nft: boolean
  created_at: string
  profiles: {
    username: string
    display_name: string | null
    avatar_url: string | null
    is_verified: boolean
  }
}

// Helper function to format duration
function formatDuration(seconds: number | null): string {
  if (!seconds) return "0:00"
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

// Helper function to transform database video to component format
function transformVideo(video: Video) {
  return {
    id: video.id,
    title: video.title,
    description: video.description || "No description available",
    thumbnail: video.thumbnail_url || "/placeholder.svg?height=720&width=1280",
    duration: formatDuration(video.duration),
    creator: video.profiles.display_name || video.profiles.username,
    isNft: video.is_nft,
    rarity: video.is_nft ? "rare" as const : undefined,
    views: video.view_count,
    likes: video.like_count,
    videoUrl: video.video_url,
    creatorAvatar: video.profiles.avatar_url,
    isVerified: video.profiles.is_verified,
  }
}

// Category icons mapping
const categoryIcons = {
  creative: Sparkles,
  shorts: Zap,
  comedy: Smile,
  encrypted: Lock,
  music: Music,
  art: Palette,
  film: Film,
  romance: Heart,
  anime: Camera,
  cartoon: Camera,
}

export default function DiscoverPage() {
  const [activeTab, setActiveTab] = useState("creative")
  const [searchQuery, setSearchQuery] = useState("")
  const [videos, setVideos] = useState<Video[]>([])
  const [featuredVideo, setFeaturedVideo] = useState<Video | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Tab labels for accessibility
  const tabLabels = {
    creative: "Creative",
    shorts: "Shorts",
    comedy: "Comedy",
    encrypted: "Encrypted",
    music: "Music",
    art: "Art",
    film: "Film",
    romance: "Romance",
    anime: "Anime",
    cartoon: "Cartoon",
  }

  // Fetch videos from database
  useEffect(() => {
    const fetchVideos = async () => {
      try {
        setIsLoading(true)

        // Fetch all videos with creator profiles
        const { data: allVideos, error } = await supabase
          .from('videos')
          .select(`
            *,
            profiles:creator_id (
              username,
              display_name,
              avatar_url,
              is_verified
            )
          `)
          .eq('is_public', true)
          .order('created_at', { ascending: false })

        if (error) {
          console.error('Error fetching videos:', error)
          return
        }

        if (allVideos && allVideos.length > 0) {
          setVideos(allVideos as Video[])

          // Select a random video as featured for the current category
          const categoryVideos = allVideos.filter(video => video.category === activeTab)
          if (categoryVideos.length > 0) {
            const randomIndex = Math.floor(Math.random() * categoryVideos.length)
            setFeaturedVideo(categoryVideos[randomIndex] as Video)
          } else {
            // If no videos in current category, pick from all videos
            const randomIndex = Math.floor(Math.random() * allVideos.length)
            setFeaturedVideo(allVideos[randomIndex] as Video)
          }
        }
      } catch (error) {
        console.error('Error in fetchVideos:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchVideos()
  }, [])

  // Update featured video when category changes
  useEffect(() => {
    if (videos.length > 0) {
      const categoryVideos = videos.filter(video => video.category === activeTab)
      if (categoryVideos.length > 0) {
        const randomIndex = Math.floor(Math.random() * categoryVideos.length)
        setFeaturedVideo(categoryVideos[randomIndex])
      }
    }
  }, [activeTab, videos])

  // Get videos for current category with proper memoization
  const currentVideos = useMemo(() => {
    let categoryVideos = videos.filter(video => video.category === activeTab)

    // Apply search filter
    if (searchQuery) {
      categoryVideos = categoryVideos.filter(video =>
        video.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        video.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        video.profiles.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
        video.profiles.display_name?.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // Remove featured video from the list and transform
    return categoryVideos
      .filter(video => video.id !== featuredVideo?.id)
      .map(transformVideo)
  }, [videos, activeTab, searchQuery, featuredVideo])

  return (
    <div className="min-h-screen bg-zinc-950">
      <Header visible={true} />
      
      <main className="container mx-auto px-4 pt-24 pb-20">
        {/* Page Header */}
        <div className="text-center mb-12">
          <div className="relative inline-block">
            {/* Decorative elements */}
            <div className="absolute -top-8 -left-8 w-16 h-16 rounded-full bg-cyan-500/10 blur-2xl"></div>
            <div className="absolute -bottom-8 -right-8 w-16 h-16 rounded-full bg-purple-500/10 blur-2xl"></div>
            
            <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-cyan-300 via-blue-400 to-purple-500 bg-clip-text text-transparent mb-4">
              Discover
            </h1>
            <p className="text-zinc-400 text-lg max-w-2xl mx-auto">
              Explore amazing AI-generated videos across different categories and find your next favorite creator
            </p>
          </div>
        </div>

        {/* Search Bar */}
        <div className="max-w-2xl mx-auto mb-12">
          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/20 to-purple-500/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
            <div className="relative bg-zinc-900/80 backdrop-blur-sm border border-zinc-800 rounded-2xl p-1">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-zinc-400" />
                <Input
                  placeholder="Search videos, creators, collections..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-12 pr-4 py-4 bg-transparent border-0 text-white placeholder:text-zinc-400 focus-visible:ring-2 focus-visible:ring-cyan-500/50 text-lg"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Category Tabs */}
        <Tabs defaultValue="creative" className="space-y-8" onValueChange={setActiveTab}>
          <div className="flex justify-center mb-8">
            <div className="overflow-x-auto scrollbar-hide">
              <TabsList className="bg-gradient-to-r from-zinc-900 via-zinc-800 to-zinc-900 border border-cyan-500/30 rounded-full p-1.5 flex gap-1 shadow-xl shadow-cyan-500/20 backdrop-blur-sm h-auto animate-glow relative min-w-max">
                {Object.entries(tabLabels).map(([value, label]) => {
                  const IconComponent = categoryIcons[value as keyof typeof categoryIcons]
                  return (
                    <TabsTrigger
                      key={value}
                      value={value}
                      className="w-12 h-12 rounded-full flex items-center justify-center group relative data-[state=active]:bg-gradient-to-br data-[state=active]:from-cyan-400 data-[state=active]:to-blue-600 data-[state=active]:shadow-lg data-[state=active]:shadow-cyan-500/30 transition-all duration-300"
                      aria-label={label}
                    >
                      <IconComponent className="h-5 w-5 group-data-[state=active]:text-white text-zinc-400 group-hover:text-zinc-200 transition-colors" />
                      <span className="sr-only">{label}</span>
                      {activeTab === value && (
                        <span className="absolute -bottom-9 left-1/2 transform -translate-x-1/2 text-xs text-cyan-400 font-medium whitespace-nowrap">
                          {label}
                        </span>
                      )}
                    </TabsTrigger>
                  )
                })}
              </TabsList>
            </div>
          </div>

          {/* Tab Content */}
          {Object.keys(tabLabels).map((category) => (
            <TabsContent key={category} value={category} className="pt-4">
              <div className="space-y-8">
                {isLoading ? (
                  <div className="text-center py-16">
                    <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                    <p className="text-zinc-400">Loading videos...</p>
                  </div>
                ) : (
                  <>
                    {/* Featured Video Section */}
                    {featuredVideo && category === activeTab && (
                      <div className="mb-8">
                        <h2 className="text-2xl font-bold text-white mb-4 flex items-center gap-2">
                          <Sparkles className="h-6 w-6 text-cyan-400" />
                          Featured in {tabLabels[category as keyof typeof tabLabels]}
                        </h2>
                        <div className="bg-gradient-to-br from-zinc-900/50 to-zinc-800/50 rounded-2xl p-6 border border-zinc-700/50">
                          <div className="grid md:grid-cols-2 gap-6 items-center">
                            <div className="aspect-video rounded-xl overflow-hidden">
                              <img
                                src={featuredVideo.thumbnail_url || "/placeholder.svg?height=360&width=640"}
                                alt={featuredVideo.title}
                                className="w-full h-full object-cover"
                              />
                            </div>
                            <div className="space-y-4">
                              <div>
                                <h3 className="text-xl font-bold text-white mb-2">{featuredVideo.title}</h3>
                                <p className="text-zinc-400 text-sm line-clamp-3">
                                  {featuredVideo.description || "No description available"}
                                </p>
                              </div>
                              <div className="flex items-center gap-4">
                                <div className="flex items-center gap-2">
                                  <img
                                    src={featuredVideo.profiles.avatar_url || "/placeholder.svg?height=32&width=32"}
                                    alt={featuredVideo.profiles.username}
                                    className="w-8 h-8 rounded-full"
                                  />
                                  <span className="text-sm text-zinc-300">
                                    {featuredVideo.profiles.display_name || featuredVideo.profiles.username}
                                  </span>
                                  {featuredVideo.profiles.is_verified && (
                                    <div className="w-4 h-4 rounded-full bg-cyan-500 flex items-center justify-center">
                                      <svg className="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                      </svg>
                                    </div>
                                  )}
                                </div>
                                <div className="text-xs text-zinc-500">
                                  {formatDuration(featuredVideo.duration)} • {featuredVideo.view_count} views
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Category Videos - Horizontal Scrollable Rows */}
                    {currentVideos.length > 0 ? (
                      <div>
                        <h2 className="text-xl font-bold text-white mb-4">
                          More in {tabLabels[category as keyof typeof tabLabels]}
                        </h2>
                        <HorizontalVideoGrid videos={currentVideos} />
                      </div>
                    ) : (
                      <div className="text-center py-16">
                        <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-zinc-800 flex items-center justify-center">
                          <Search className="h-8 w-8 text-zinc-400" />
                        </div>
                        <h3 className="text-xl font-semibold text-zinc-300 mb-2">No videos found</h3>
                        <p className="text-zinc-500">
                          {searchQuery
                            ? `No results for "${searchQuery}" in ${tabLabels[category as keyof typeof tabLabels]}`
                            : `No videos available in ${tabLabels[category as keyof typeof tabLabels]} category yet`
                          }
                        </p>
                      </div>
                    )}
                  </>
                )}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </main>
    </div>
  )
}
